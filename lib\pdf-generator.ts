import puppeteer from 'puppeteer'

export interface PDFGenerationOptions {
  format?: 'A4' | 'Letter'
  landscape?: boolean
  margin?: {
    top?: string
    right?: string
    bottom?: string
    left?: string
  }
  printBackground?: boolean
  quality?: number
}

export class PDFGenerator {
  private static instance: PDFGenerator
  private browser: import('puppeteer').Browser | null = null

  private constructor() {}

  public static getInstance(): PDFGenerator {
    if (!PDFGenerator.instance) {
      PDFGenerator.instance = new PDFGenerator()
    }
    return PDFGenerator.instance
  }

  private async initBrowser(): Promise<puppeteer.Browser> {
    if (!this.browser) {
      console.log('Initializing Puppeteer browser...')
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu'
        ]
      })
      console.log('Puppeteer browser initialized successfully')
    }
    return this.browser
  }

  public async generatePDFFromHTML(
    html: string,
    options: PDFGenerationOptions = {}
  ): Promise<Buffer> {
    const browser = await this.initBrowser()
    const page = await browser.newPage()

    try {
      console.log('Setting page content...')
      
      // Set viewport for consistent rendering
      await page.setViewport({
        width: 1200,
        height: 850,
        deviceScaleFactor: 2
      })

      // Set content and wait for fonts to load
      await page.setContent(html, {
        waitUntil: ['networkidle0', 'domcontentloaded']
      })

      // Wait for fonts to load
      await page.evaluateHandle('document.fonts.ready')

      // Additional wait to ensure all styles are applied
      await new Promise(resolve => setTimeout(resolve, 1000))

      console.log('Generating PDF...')

      const pdfOptions: puppeteer.PDFOptions = {
        format: options.format || 'A4',
        landscape: options.landscape || true, // Landscape for certificate
        printBackground: options.printBackground !== false,
        margin: options.margin || {
          top: '0.5in',
          right: '0.5in',
          bottom: '0.5in',
          left: '0.5in'
        },
        preferCSSPageSize: true
      }

      const pdfBuffer = await page.pdf(pdfOptions)
      
      console.log(`PDF generated successfully, size: ${pdfBuffer.length} bytes`)
      
      return pdfBuffer

    } catch (error) {
      console.error('Error generating PDF:', error)
      throw new Error(`PDF generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      await page.close()
    }
  }

  public async generateCertificatePDF(html: string): Promise<Buffer> {
    return this.generatePDFFromHTML(html, {
      format: 'A4',
      landscape: true,
      printBackground: true,
      margin: {
        top: '0.2in',
        right: '0.2in',
        bottom: '0.2in',
        left: '0.2in'
      }
    })
  }

  public async closeBrowser(): Promise<void> {
    if (this.browser) {
      console.log('Closing Puppeteer browser...')
      await this.browser.close()
      this.browser = null
      console.log('Puppeteer browser closed')
    }
  }

  // Cleanup method for graceful shutdown
  public static async cleanup(): Promise<void> {
    if (PDFGenerator.instance) {
      await PDFGenerator.instance.closeBrowser()
    }
  }
}

// Export a convenience function for quick PDF generation
export async function generatePDFFromHTML(
  html: string,
  options?: PDFGenerationOptions
): Promise<Buffer> {
  const generator = PDFGenerator.getInstance()
  return generator.generatePDFFromHTML(html, options)
}

// Export a convenience function for certificate PDF generation
export async function generateCertificatePDF(html: string): Promise<Buffer> {
  const generator = PDFGenerator.getInstance()
  return generator.generateCertificatePDF(html)
}

// Graceful shutdown handler
process.on('SIGINT', async () => {
  console.log('Received SIGINT, cleaning up PDF generator...')
  await PDFGenerator.cleanup()
  process.exit(0)
})

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM, cleaning up PDF generator...')
  await PDFGenerator.cleanup()
  process.exit(0)
})
