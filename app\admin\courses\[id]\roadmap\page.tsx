'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useParams, useRouter } from 'next/navigation'
import { ArrowLeftIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'
import RoadmapConfig from '@/components/admin/roadmap/roadmap-config'
import { toast } from 'react-hot-toast'

interface RoadmapConfig {
  hasRoadmap: boolean
  roadmapTitle?: string
  roadmapDescription?: string
  missions: any[]
}

interface Course {
  id: string
  title: string
  description?: string
}

export default function CourseRoadmapPage() {
  const params = useParams()
  const router = useRouter()
  const courseId = params?.id as string

  const [course, setCourse] = useState<Course | null>(null)
  const [roadmapConfig, setRoadmapConfig] = useState<RoadmapConfig>({
    hasRoadmap: false,
    roadmapTitle: '',
    roadmapDescription: '',
    missions: []
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (courseId) {
      fetchCourseAndRoadmap()
    }
  }, [courseId])

  const fetchCourseAndRoadmap = async () => {
    try {
      setLoading(true)
      
      // Fetch course details
      const courseResponse = await fetch(`/api/admin/courses/${courseId}`)
      if (courseResponse.ok) {
        const courseData = await courseResponse.json()
        setCourse(courseData.data)
      }

      // Fetch roadmap configuration
      const roadmapResponse = await fetch(`/api/admin/courses/${courseId}/roadmap`)
      if (roadmapResponse.ok) {
        const roadmapData = await roadmapResponse.json()
        setRoadmapConfig(roadmapData.data || {
          hasRoadmap: false,
          roadmapTitle: '',
          roadmapDescription: '',
          missions: []
        })
      }
    } catch (error) {
      console.error('Failed to fetch course and roadmap data:', error)
      toast.error('Failed to load course data')
    } finally {
      setLoading(false)
    }
  }

  const handleRoadmapUpdate = async (config: RoadmapConfig) => {
    try {
      setSaving(true)
      const response = await fetch(`/api/admin/courses/${courseId}/roadmap`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to update roadmap')
      }

      setRoadmapConfig(config)
      toast.success('Roadmap configuration saved!')
    } catch (error: any) {
      console.error('Error updating roadmap:', error)
      toast.error(error.message || 'Failed to save roadmap configuration')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Course Not Found</h1>
          <Link href="/admin/courses">
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              Back to Courses
            </button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href={`/admin/courses/${courseId}`}>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="p-2 bg-white/60 backdrop-blur-xl rounded-xl border border-white/20 shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  <ArrowLeftIcon className="w-5 h-5 text-gray-600" />
                </motion.button>
              </Link>
              
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Learning Roadmap
                </h1>
                <p className="text-gray-600 mt-1">
                  Configure gamified learning journey for "{course.title}"
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Link href={`/admin/courses/${courseId}/edit`}>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-4 py-2 bg-white/60 backdrop-blur-xl rounded-xl border border-white/20 shadow-lg hover:shadow-xl transition-all duration-200 text-gray-700"
                >
                  Edit Course
                </motion.button>
              </Link>
              
              <Link href={`/admin/courses/${courseId}`}>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  View Course
                </motion.button>
              </Link>
            </div>
          </div>
        </motion.div>

        {/* Roadmap Configuration */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <RoadmapConfig
            courseId={courseId}
            hasRoadmap={roadmapConfig.hasRoadmap}
            roadmapTitle={roadmapConfig.roadmapTitle}
            roadmapDescription={roadmapConfig.roadmapDescription}
            missions={roadmapConfig.missions}
            onUpdate={handleRoadmapUpdate}
          />
        </motion.div>

        {/* Save Status */}
        {saving && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2"
          >
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            <span>Saving...</span>
          </motion.div>
        )}
      </div>
    </div>
  )
}
