'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  PlusIcon, 
  TrashIcon, 
  PencilIcon,
  MapIcon,
  StarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowRightIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { toast } from 'react-hot-toast'
import MissionModal from './mission-modal'

interface Mission {
  id: string
  title: string
  description?: string
  icon?: string
  color?: string
  order: number
  xPosition?: number
  yPosition?: number
  isRequired: boolean
  pointsReward: number
  badgeReward?: string
  estimatedTime?: string
  contents: MissionContent[]
  prerequisites: string[]
}

interface MissionContent {
  id: string
  contentId: string
  contentType: 'LESSON' | 'QUIZ' | 'ASSIGNMENT' | 'DISCUSSION'
  order: number
  isRequired: boolean
  title?: string
}

interface RoadmapConfigProps {
  courseId: string
  hasRoadmap: boolean
  roadmapTitle?: string
  roadmapDescription?: string
  missions: Mission[]
  onUpdate: (config: {
    hasRoadmap: boolean
    roadmapTitle?: string
    roadmapDescription?: string
    missions: Mission[]
  }) => void
}

export default function RoadmapConfig({
  courseId,
  hasRoadmap,
  roadmapTitle,
  roadmapDescription,
  missions,
  onUpdate
}: RoadmapConfigProps) {
  const [localConfig, setLocalConfig] = useState({
    hasRoadmap,
    roadmapTitle: roadmapTitle || '',
    roadmapDescription: roadmapDescription || '',
    missions
  })
  const [editingMission, setEditingMission] = useState<Mission | null>(null)
  const [showMissionModal, setShowMissionModal] = useState(false)
  const [loading, setLoading] = useState(false)
  const [autoSaving, setAutoSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  const handleToggleRoadmap = (enabled: boolean) => {
    setLocalConfig(prev => ({ ...prev, hasRoadmap: enabled }))
    onUpdate({ ...localConfig, hasRoadmap: enabled })
  }

  const handleConfigChange = (field: string, value: string) => {
    setLocalConfig(prev => ({ ...prev, [field]: value }))

    // Auto-save after 2 seconds of inactivity
    setTimeout(() => {
      handleAutoSave()
    }, 2000)
  }

  const handleAutoSave = async () => {
    if (autoSaving || loading) return

    setAutoSaving(true)
    try {
      await onUpdate(localConfig)
      setLastSaved(new Date())
    } catch (error) {
      // Silent fail for auto-save
      console.error('Auto-save failed:', error)
    } finally {
      setAutoSaving(false)
    }
  }

  const handleSaveConfig = async () => {
    if (!localConfig.roadmapTitle?.trim() && localConfig.hasRoadmap) {
      toast.error('Please enter a roadmap title')
      return
    }

    setLoading(true)
    try {
      await onUpdate(localConfig)
      toast.success('Roadmap configuration saved!')
    } catch (error: any) {
      toast.error(error.message || 'Failed to save roadmap configuration')
    } finally {
      setLoading(false)
    }
  }

  const handleAddMission = () => {
    setEditingMission(null)
    setShowMissionModal(true)
  }

  const handleEditMission = (mission: Mission) => {
    setEditingMission(mission)
    setShowMissionModal(true)
  }

  const handleDeleteMission = (missionId: string) => {
    const updatedMissions = localConfig.missions.filter(m => m.id !== missionId)
    setLocalConfig(prev => ({ ...prev, missions: updatedMissions }))
    onUpdate({ ...localConfig, missions: updatedMissions })
    toast.success('Mission deleted')
  }

  const handleSaveMission = (mission: Mission) => {
    let updatedMissions
    if (editingMission) {
      updatedMissions = localConfig.missions.map(m => 
        m.id === mission.id ? mission : m
      )
    } else {
      updatedMissions = [...localConfig.missions, { 
        ...mission, 
        id: `mission_${Date.now()}`,
        order: localConfig.missions.length + 1
      }]
    }
    
    setLocalConfig(prev => ({ ...prev, missions: updatedMissions }))
    onUpdate({ ...localConfig, missions: updatedMissions })
    setShowMissionModal(false)
    toast.success(editingMission ? 'Mission updated' : 'Mission created')
  }

  return (
    <div className="space-y-6">
      {/* Roadmap Toggle */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <MapIcon className="h-6 w-6 text-blue-500" />
              <div>
                <CardTitle>Gamified Learning Roadmap</CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  Create an interactive, game-like learning journey for your students
                </p>
              </div>
            </div>
            <Switch
              checked={localConfig.hasRoadmap}
              onCheckedChange={handleToggleRoadmap}
            />
          </div>
        </CardHeader>
        
        {localConfig.hasRoadmap && (
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Roadmap Title
                </label>
                <input
                  type="text"
                  value={localConfig.roadmapTitle}
                  onChange={(e) => handleConfigChange('roadmapTitle', e.target.value)}
                  placeholder="e.g., Master JavaScript Journey"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Estimated Completion
                </label>
                <input
                  type="text"
                  placeholder="e.g., 8 weeks"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Roadmap Description
              </label>
              <textarea
                value={localConfig.roadmapDescription}
                onChange={(e) => handleConfigChange('roadmapDescription', e.target.value)}
                placeholder="Describe the learning journey and what students will achieve..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div className="flex items-center space-x-4">
              <Button onClick={handleSaveConfig} disabled={loading}>
                {loading ? 'Saving...' : 'Save Configuration'}
              </Button>

              {/* Save Status Indicator */}
              <div className="text-sm text-gray-500">
                {autoSaving && (
                  <span className="flex items-center">
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-500 mr-2"></div>
                    Auto-saving...
                  </span>
                )}
                {lastSaved && !autoSaving && (
                  <span>
                    Saved {lastSaved.toLocaleTimeString()}
                  </span>
                )}
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Missions Management */}
      {localConfig.hasRoadmap && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <StarIcon className="h-5 w-5 text-yellow-500" />
                <span>Learning Missions</span>
              </CardTitle>
              <Button onClick={handleAddMission} size="sm">
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Mission
              </Button>
            </div>
          </CardHeader>
          
          <CardContent>
            {localConfig.missions.length === 0 ? (
              <div className="text-center py-8">
                <MapIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No missions yet</h3>
                <p className="text-gray-600 mb-4">
                  Create your first mission to start building the learning roadmap
                </p>
                <Button onClick={handleAddMission}>
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Create First Mission
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {localConfig.missions
                  .sort((a, b) => a.order - b.order)
                  .map((mission, index) => (
                    <MissionCard
                      key={mission.id}
                      mission={mission}
                      index={index}
                      onEdit={() => handleEditMission(mission)}
                      onDelete={() => handleDeleteMission(mission.id)}
                    />
                  ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Mission Modal */}
      <MissionModal
        isOpen={showMissionModal}
        onClose={() => setShowMissionModal(false)}
        mission={editingMission}
        courseId={courseId}
        existingMissions={localConfig.missions}
        onSave={handleSaveMission}
      />
    </div>
  )
}

interface MissionCardProps {
  mission: Mission
  index: number
  onEdit: () => void
  onDelete: () => void
}

function MissionCard({ mission, index, onEdit, onDelete }: MissionCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <div 
              className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold"
              style={{ backgroundColor: mission.color || '#3B82F6' }}
            >
              {mission.icon || (index + 1)}
            </div>
          </div>
          
          <div className="flex-1">
            <h4 className="font-medium text-gray-900">{mission.title}</h4>
            {mission.description && (
              <p className="text-sm text-gray-600 mt-1">{mission.description}</p>
            )}
            
            <div className="flex items-center space-x-4 mt-2">
              <Badge variant={mission.isRequired ? "default" : "secondary"}>
                {mission.isRequired ? 'Required' : 'Optional'}
              </Badge>
              
              {mission.pointsReward > 0 && (
                <div className="flex items-center text-sm text-yellow-600">
                  <StarIcon className="h-4 w-4 mr-1" />
                  {mission.pointsReward} points
                </div>
              )}
              
              {mission.estimatedTime && (
                <div className="flex items-center text-sm text-gray-500">
                  <ClockIcon className="h-4 w-4 mr-1" />
                  {mission.estimatedTime}
                </div>
              )}
              
              <div className="text-sm text-gray-500">
                {mission.contents.length} content items
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" onClick={onEdit}>
            <PencilIcon className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={onDelete}>
            <TrashIcon className="h-4 w-4 text-red-500" />
          </Button>
        </div>
      </div>
    </motion.div>
  )
}
